WITH base_persons AS (
    SELECT
        person_id as ph_person_id
        , properties:"$initial_referrer"::string as referring_domain
        , properties:email::string as email_address
        , properties:"$initial_current_url"::string as initial_landing_url
        , properties
        , person_id
    FROM {{ ref('stg_posthog_persons') }}
),
ranked_persons AS (
    SELECT
        ph_person_id
        , referring_domain
        , email_address
        , initial_landing_url
        , properties
        , CASE
            WHEN email_address IS NOT NULL THEN
                ROW_NUMBER() OVER (
                    PARTITION BY email_address
                    ORDER BY person_id ASC  -- earliest record per email address
                )
            ELSE 1  -- All records without email get rank 1 (keep all of them)
        END AS rn
    FROM base_persons
),
domain_standardization AS (
    SELECT
        ph_person_id
        , referring_domain
        , email_address
        , initial_landing_url
        , properties
        , CASE
            WHEN referring_domain = '$direct' THEN 'direct landing or incognito'
            WHEN referring_domain ILIKE '%instagram%' THEN 'instagram'
            WHEN referring_domain ILIKE '%superlink%' THEN 'superlink'
            WHEN referring_domain ILIKE '%fanfix%' THEN 'fanfix'
            WHEN referring_domain ILIKE '%google%' THEN 'google'
            WHEN referring_domain ILIKE '%tiktok%' THEN 'tiktok'
            WHEN referring_domain ILIKE '%youtube%' THEN 'youtube'
            WHEN referring_domain ILIKE '%facebook%' THEN 'facebook'
            WHEN referring_domain ILIKE '%reddit%' THEN 'reddit'
            WHEN referring_domain ILIKE '%yahoo%' THEN 'yahoo'
            WHEN referring_domain ILIKE '%patreon%' THEN 'patreon'
            WHEN referring_domain ILIKE '%telegram%' THEN 'telegram'
            WHEN referring_domain ILIKE '%twitch%' THEN 'twitch'
            WHEN referring_domain ILIKE '%twitter%' OR referring_domain ILIKE '%t.co%' THEN 'twitter'
            WHEN referring_domain ILIKE '%pinterest%' THEN 'pinterest'
            WHEN referring_domain ILIKE '%yandex%' THEN 'yandex'
            WHEN referring_domain ILIKE '%x.com%' THEN 'twitter'
            WHEN referring_domain ILIKE '%linktr%'
                    OR referring_domain ILIKE '%liinks%'
                    OR referring_domain ILIKE '%lnk.bio%'
                    OR referring_domain ILIKE '%link.me%'
                THEN 'Linktree or equivalent'
            ELSE referring_domain
        END AS standardized_referring_domain
    FROM ranked_persons
    WHERE rn = 1
)

SELECT
    ph_person_id
    , referring_domain
    , email_address
    , initial_landing_url
    , standardized_referring_domain
    , properties
FROM domain_standardization
