-- Performance Baseline Analysis for PostHog Data

-- Data Volume Analysis
SELECT 
    'Data Volume Analysis' as test_type,
    COUNT(*) as total_rows,
    COUNT(DISTINCT properties:email::string) as unique_emails,
    COUNT(CASE WHEN properties:email IS NOT NULL THEN 1 END) as rows_with_email,
    COUNT(CASE WHEN properties:email IS NULL THEN 1 END) as rows_without_email,
    ROUND(COUNT(CASE WHEN properties:email IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as pct_with_email,
    0 as max_duplicates_per_email,
    0.0 as avg_records_per_email
FROM {{ ref('stg_posthog_persons') }}

UNION ALL

-- Email Duplication Analysis  
SELECT 
    'Email Duplication Analysis' as test_type,
    COUNT(*) as total_email_records,
    COUNT(DISTINCT properties:email::string) as unique_emails,
    COUNT(*) - COUNT(DISTINCT properties:email::string) as duplicate_records,
    ROUND((COUNT(*) - COUNT(DISTINCT properties:email::string)) * 100.0 / COUNT(*), 2) as pct_duplicates,
    0.0 as pct_with_email,
    MAX(email_count) as max_duplicates_per_email,
    AVG(email_count) as avg_records_per_email
FROM (
    SELECT 
        properties:email::string as email,
        COUNT(*) as email_count
    FROM {{ ref('stg_posthog_persons') }}
    WHERE properties:email IS NOT NULL
    GROUP BY properties:email::string
) email_counts
