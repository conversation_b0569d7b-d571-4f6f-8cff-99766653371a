-- Performance Testing Framework for dim_fan_ph_profile
-- Run this to get baseline metrics before optimization

-- 1. Data Volume Analysis
SELECT 
    'Data Volume Analysis' as test_type,
    COUNT(*) as total_rows,
    COUNT(DISTINCT properties:email::string) as unique_emails,
    COUNT(CASE WHEN properties:email IS NOT NULL THEN 1 END) as rows_with_email,
    COUNT(CASE WHEN properties:email IS NULL THEN 1 END) as rows_without_email,
    ROUND(COUNT(CASE WHEN properties:email IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as pct_with_email
FROM {{ ref('stg_posthog_persons') }}

UNION ALL

-- 2. Email Duplication Analysis  
SELECT 
    'Email Duplication Analysis' as test_type,
    COUNT(*) as total_email_records,
    COUNT(DISTINCT properties:email::string) as unique_emails,
    COUNT(*) - COUNT(DISTINCT properties:email::string) as duplicate_records,
    ROUND((COUNT(*) - COUNT(DISTINCT properties:email::string)) * 100.0 / COUNT(*), 2) as pct_duplicates,
    MAX(email_count) as max_duplicates_per_email,
    AVG(email_count) as avg_records_per_email
FROM (
    SELECT 
        properties:email::string as email,
        COUNT(*) as email_count
    FROM {{ ref('stg_posthog_persons') }}
    WHERE properties:email IS NOT NULL
    GROUP BY properties:email::string
) email_counts

UNION ALL

-- 3. Property Extraction Performance Test
SELECT 
    'Property Extraction Test' as test_type,
    COUNT(DISTINCT properties:"$initial_referrer"::string) as unique_referrers,
    COUNT(DISTINCT properties:"$initial_current_url"::string) as unique_urls,
    COUNT(CASE WHEN properties:"$initial_referrer" IS NOT NULL THEN 1 END) as rows_with_referrer,
    COUNT(CASE WHEN properties:"$initial_current_url" IS NOT NULL THEN 1 END) as rows_with_url,
    0 as pct_with_email,
    0 as max_duplicates_per_email
FROM {{ ref('stg_posthog_persons') }}
